/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.c
  * @brief   This file provides code for the configuration
  *          of all used GPIO pins.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/** Configure pins as
        * Analog
        * Input
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOC, LED1_Pin, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, CS_Pin|V_OUT_Pin, GPIO_PIN_RESET);

  // 注意：RF_PWR_Pin, GPS_PWR_Pin, CAM_PW_Pin不在这里设置初始电平
  // 因为它们会被设置为输入模式，利用上拉电阻确保PMOS管关闭

  /*Configure GPIO pins : LED1_Pin (CAM_PW_Pin单独配置) */
  GPIO_InitStruct.Pin = LED1_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

  /*Configure GPIO pins : CS_Pin V_OUT_Pin */
  GPIO_InitStruct.Pin = CS_Pin|V_OUT_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pins : GPS_PWR_Pin RF_PWR_Pin */
  GPIO_InitStruct.Pin = GPS_PWR_Pin|RF_PWR_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_PULLUP;  // 上拉电阻确保PMOS管关闭
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /*Configure GPIO pin : PB5 */
  GPIO_InitStruct.Pin = GPIO_PIN_5;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_PULLDOWN;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /*Configure GPIO pin : CAM_PW_Pin */
  GPIO_InitStruct.Pin = CAM_PW_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_PULLUP;  // 上拉电阻确保PMOS管关闭
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

}

/* USER CODE BEGIN 2 */

/**
 * @brief RF电源引脚设为输入模式（关闭电源）
 * @note 利用上拉电阻提高电压，确保PMOS管彻底关闭
 */
void RF_PowerPin_SetInput(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = RF_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // 上拉电阻确保PMOS管关闭
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief RF电源引脚设为输出低电平（开启电源）
 */
void RF_PowerPin_SetOutputLow(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 先设置输出电平为低
    HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_RESET);

    GPIO_InitStruct.Pin = RF_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief RF电源引脚设为输出高电平（关闭电源）
 */
void RF_PowerPin_SetOutputHigh(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 先设置输出电平为高
    HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_SET);

    GPIO_InitStruct.Pin = RF_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief GPS电源引脚设为输入模式（关闭电源）
 * @note 利用上拉电阻提高电压，确保PMOS管彻底关闭
 */
void GPS_PowerPin_SetInput(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = GPS_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // 上拉电阻确保PMOS管关闭
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief GPS电源引脚设为输出低电平（开启电源）
 */
void GPS_PowerPin_SetOutputLow(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 先设置输出电平为低
    HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_RESET);

    GPIO_InitStruct.Pin = GPS_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief GPS电源引脚设为输出高电平（关闭电源）
 */
void GPS_PowerPin_SetOutputHigh(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 先设置输出电平为高
    HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_SET);

    GPIO_InitStruct.Pin = GPS_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
}

/**
 * @brief 摄像头电源引脚设为输入模式（关闭电源）
 * @note 利用上拉电阻提高电压，确保PMOS管彻底关闭
 */
void CAM_PowerPin_SetInput(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = CAM_PW_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // 上拉电阻确保PMOS管关闭
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief 摄像头电源引脚设为输出低电平（开启电源）
 */
void CAM_PowerPin_SetOutputLow(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 先设置输出电平为低
    HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_RESET);

    GPIO_InitStruct.Pin = CAM_PW_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief 摄像头电源引脚设为输出高电平（关闭电源）
 */
void CAM_PowerPin_SetOutputHigh(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 先设置输出电平为高
    HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_SET);

    GPIO_InitStruct.Pin = CAM_PW_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
 * @brief 所有电源引脚设为输入模式（休眠前调用）
 * @note 进入休眠前调用，利用上拉电阻确保所有PMOS管彻底关闭
 */
void PowerPins_SetAllInput(void)
{
    RF_PowerPin_SetInput();   // RF模块电源引脚设为输入
    GPS_PowerPin_SetInput();  // GPS模块电源引脚设为输入
    CAM_PowerPin_SetInput();  // 摄像头模块电源引脚设为输入
}

/**
 * @brief 电源引脚初始化为输出模式（唤醒后调用）
 * @note 唤醒后调用，初始化为输出模式并关闭所有电源
 */
void PowerPins_InitForWakeup(void)
{
    // 初始化为输出高电平，关闭所有电源
    RF_PowerPin_SetOutputHigh();   // RF模块电源关闭
    GPS_PowerPin_SetOutputHigh();  // GPS模块电源关闭
    CAM_PowerPin_SetInput();       // 摄像头模块电源保持输入模式（暂时不启用）
}

/* USER CODE END 2 */
