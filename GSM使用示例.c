/**
 * @file    GSM使用示例.c
 * @brief   GSM模块使用示例代码
 * @note    此文件仅作为使用示例，不包含在项目中
 */

#include "GSM.h"
#include <stdio.h>

/**
 * @brief  GSM模块使用示例
 */
void GSM_UsageExample(void)
{
    GSM_Status_t status;
    GSM_Info_t gsm_info;

    printf("GSM Module Usage Example\r\n");

    // 1. 初始化GSM模块
    printf("1. Initializing GSM module...\r\n");
    status = GSM_Init();
    if (status != GSM_OK) {
        printf("GSM Init Failed! Status: %d\r\n", status);
        return;
    }
    printf("GSM Init Success!\r\n");

    // 2. 获取模块信息
    printf("2. Getting module information...\r\n");
    status = GSM_GetInfo(&gsm_info);
    if (status == GSM_OK) {
        printf("Model: %s\r\n", gsm_info.model);
        printf("CCID: %s\r\n", gsm_info.ccid);
        printf("Voltage: %d mV\r\n", gsm_info.voltage);
        printf("Signal: %d\r\n", gsm_info.signal);
        printf("Network Reg: %d\r\n", gsm_info.network_reg);
    } else {
        printf("Get Info Failed! Status: %d\r\n", status);
    }

    // 3. 检查网络状态
    printf("3. Checking network status...\r\n");
    uint8_t network_status;
    status = GSM_CheckNetwork(&network_status);
    if (status == GSM_OK) {
        if (network_status == 1) {
            printf("Network registered successfully!\r\n");
        } else {
            printf("Network not registered! Status: %d\r\n", network_status);
        }
    } else {
        printf("Check network failed! Status: %d\r\n", status);
    }

    // 4. 连接TCP服务器
    printf("4. Connecting to TCP server...\r\n");
    status = GSM_ConnectServer();
    if (status == GSM_OK) {
        printf("Connected to server successfully!\r\n");

        // 5. 设置快发模式
        printf("5. Setting quick send mode...\r\n");
        status = GSM_SetQuickSend();
        if (status == GSM_OK) {
            printf("Quick send mode set successfully!\r\n");

            // 6. 发送数据
            printf("6. Sending data...\r\n");
            const char* test_data = "HY122S+11957.70898+3016.57080+052857.270625+38.9+1+6+1.7+4.20+55.6+2.9+3.0+3.8+-1.9+0.0+0.00+-128+89860316249511500582+E";
            status = GSM_SendData(test_data, strlen(test_data));
            if (status == GSM_OK) {
                printf("Data sent successfully!\r\n");
            } else {
                printf("Send data failed! Status: %d\r\n", status);
            }
        } else {
            printf("Set quick send mode failed! Status: %d\r\n", status);
        }

        // 7. 关闭服务器连接
        printf("7. Closing server connection...\r\n");
        status = GSM_CloseServer();
        if (status == GSM_OK) {
            printf("Server connection closed!\r\n");
        } else {
            printf("Close server failed! Status: %d\r\n", status);
        }
    } else {
        printf("Connect to server failed! Status: %d\r\n", status);
    }

    printf("GSM Usage Example Completed!\r\n");
}

/**
 * @brief  定期发送数据示例
 */
void GSM_PeriodicSendExample(void)
{
    static uint32_t last_send_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每30秒发送一次数据
    if (current_time - last_send_time >= 30000) {
        GSM_State_t state = GSM_GetState();

        if (state == GSM_STATE_READY) {
            // 连接服务器
            if (GSM_ConnectServer() == GSM_OK) {
                // 发送数据
                const char* data = "Periodic data from device";
                GSM_SendData(data, strlen(data));

                // 关闭连接
                GSM_CloseServer();
            }
        }

        last_send_time = current_time;
    }
}

/**
 * @brief  获取信号强度示例
 */
void GSM_GetSignalExample(void)
{
    uint8_t signal;
    GSM_Status_t status = GSM_GetSignal(&signal);

    if (status == GSM_OK) {
        printf("Signal strength: %d\r\n", signal);

        // 信号强度判断
        if (signal >= 20) {
            printf("Signal: Excellent\r\n");
        } else if (signal >= 15) {
            printf("Signal: Good\r\n");
        } else if (signal >= 10) {
            printf("Signal: Fair\r\n");
        } else {
            printf("Signal: Poor\r\n");
        }
    } else {
        printf("Get signal failed! Status: %d\r\n", status);
    }
}

/**
 * @brief  获取电压信息示例
 */
void GSM_GetVoltageExample(void)
{
    uint16_t voltage;
    GSM_Status_t status = GSM_GetVoltage(&voltage);

    if (status == GSM_OK) {
        printf("Battery voltage: %d mV\r\n", voltage);

        // 电压判断
        if (voltage >= 3500) {
            printf("Battery: High\r\n");
        } else if (voltage >= 3000) {
            printf("Battery: Medium\r\n");
        } else {
            printf("Battery: Low\r\n");
        }
    } else {
        printf("Get voltage failed! Status: %d\r\n", status);
    }
}
