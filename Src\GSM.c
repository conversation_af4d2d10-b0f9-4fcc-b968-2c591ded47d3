/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    GSM.c
  * @brief   GSM模块AT指令控制实现
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "GSM.h"
#include "gpio.h"
#include "globals.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* 私有变量定义 */
static GSM_State_t gsm_state = GSM_STATE_INIT;
static char gsm_rx_buffer[GSM_RX_BUFFER_SIZE];  // 接收缓冲区

/* 私有函数声明 */
static uint16_t GSM_CalculateDataLength(const char* data);
static void GSM_PrintError(const char* command);
static uint16_t GSM_ReceiveData(uint32_t timeout);
static void GSM_PrintResponse(const char* command, const char* response);

/* USER CODE BEGIN 1 */

/**
 * @brief  GSM电源开启
 */
void GSM_PowerOn(void)
{
    RF_PWR_ON;  // 使用gpio.h中定义的宏
    printf("GSM power ON\r\n");
}

/**
 * @brief  GSM电源关闭
 */
void GSM_PowerOff(void)
{
    RF_PWR_OFF;  // 使用gpio.h中定义的宏
    printf("GSM power OFF\r\n");
}

/**
 * @brief  GSM完整初始化流程（包含电源控制和等待）
 * @retval GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_FullInit(void)
{
    printf("GSM full initialization started\r\n");

    // 开启GSM电源
    GSM_PowerOn();

    // 等待GSM模块启动（至少5秒）
    printf("Waiting %d ms for GSM module startup...\r\n", GSM_POWER_ON_DELAY_MS);
    HAL_Delay(GSM_POWER_ON_DELAY_MS);

    // 执行GSM初始化
    GSM_Status_t status = GSM_Init();
    if (status == GSM_OK) {
        printf("GSM full initialization completed successfully\r\n");
    } else {
        printf("GSM full initialization failed with status: %d\r\n", status);
    }

    return status;
}

/* USER CODE END 1 */

/**
 * @brief  GSM模块初始化
 * @retval GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_Init(void)
{
    GSM_Status_t status;

    // 重置接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    printf("Starting GSM AT command sequence...\r\n");

    // 1. 关闭回显
    status = GSM_SendATCommandWithRetry("ATE0", "OK", GSM_AT_TIMEOUT_MS, GSM_AT_RETRY_COUNT);
    if (status != GSM_OK) {
        printf("Failed to disable echo\r\n");
        gsm_state = GSM_STATE_ERROR;
        return status;
    }

    // 2. 获取CCID号并保存到全局变量
    status = GSM_SendATCommandWithRetry("AT+CCID", "OK", GSM_AT_TIMEOUT_MS, GSM_AT_RETRY_COUNT);
    if (status == GSM_OK) {
        // 解析CCID并保存到全局变量
        // 查找数字开头的CCID（通常是20位数字）
        char* ptr = gsm_rx_buffer;
        while (*ptr) {
            if (*ptr >= '0' && *ptr <= '9') {
                // 找到数字开头，提取CCID
                char* start = ptr;
                while (*ptr >= '0' && *ptr <= '9') {
                    ptr++;
                }
                uint16_t len = ptr - start;
                if (len >= 15 && len <= 20 && len < sizeof(gsm_ccid)) {  // CCID通常15-20位
                    strncpy(gsm_ccid, start, len);
                    gsm_ccid[len] = '\0';
                    printf("CCID obtained: %s\r\n", gsm_ccid);
                    break;
                }
            }
            ptr++;
        }
    } else {
        printf("Failed to get CCID, using default\r\n");
    }

    // 3. 获取模块电压
    uint16_t voltage = 0;
    status = GSM_GetVoltage(&voltage);
    if (status == GSM_OK) {
        printf("GSM module voltage: %d mV\r\n", voltage);
    } else {
        printf("Failed to get module voltage\r\n");
    }

    // 4. 查询网络注册状态
    uint8_t network_reg = 0;
    status = GSM_CheckNetwork(&network_reg);
    if (status == GSM_OK) {
        printf("Network registration status: %d\r\n", network_reg);
    } else {
        printf("Failed to check network registration\r\n");
    }

    // 5. 获取信号强度并保存到全局变量
    uint8_t signal = 0;
    status = GSM_GetSignal(&signal);
    if (status == GSM_OK) {
        gsm_signal_quality = (int8_t)signal;
        printf("Signal strength: %d\r\n", signal);
    } else {
        printf("Failed to get signal strength\r\n");
        gsm_signal_quality = -128;  // 表示无效
    }

    gsm_state = GSM_STATE_READY;
    printf("GSM initialization completed\r\n");
    return GSM_OK;
}

/**
 * @brief  关闭回显
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CloseEcho(void)
{
    return GSM_SendATCommand("ATE0", "OK", GSM_AT_TIMEOUT_MS);
}

/**
 * @brief  获取模块型号
 * @param  model: 型号字符串缓冲区
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetModel(char* model)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CGMM", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的型号信息
        char* start = strstr(gsm_rx_buffer, "+CGMM: ");
        if (start) {
            start += 7;  // 跳过"+CGMM: "
            char* end = strchr(start, '\r');
            if (end) {
                uint16_t len = end - start;
                if (len > 0) {
                    strncpy(model, start, len);
                    model[len] = '\0';
                }
            }
        }
    }
    return status;
}

/**
 * @brief  获取CCID号
 * @param  ccid: CCID字符串缓冲区
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetCCID(char* ccid)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CCID", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的CCID信息
        char* start = strstr(gsm_rx_buffer, "AT+CCID");
        if (start) {
            start = strchr(start, '\r');
            if (start) {
                start = strchr(start + 1, '\r');
                if (start) {
                    start += 2;  // 跳过"\r\n"
                    char* end = strchr(start, '\r');
                    if (end) {
                        uint16_t len = end - start;
                        if (len > 0 && len < 32) {
                            strncpy(ccid, start, len);
                            ccid[len] = '\0';
                        }
                    }
                }
            }
        }
    }
    return status;
}

/**
 * @brief  获取模块电压
 * @param  voltage: 电压值指针(mV)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetVoltage(uint16_t* voltage)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CBC", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的电压信息
        char* start = strstr(gsm_rx_buffer, "+CBC: ");
        if (start) {
            start += 6;  // 跳过"+CBC: "
            *voltage = (uint16_t)atoi(start);
        }
    }
    return status;
}

/**
 * @brief  查询网络注册状态
 * @param  reg_status: 注册状态指针
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CheckNetwork(uint8_t* reg_status)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CREG?", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的注册状态
        char* start = strstr(gsm_rx_buffer, "+CREG: ");
        if (start) {
            start += 7;  // 跳过"+CREG: "
            char* comma = strchr(start, ',');
            if (comma) {
                comma++;  // 跳过逗号
                *reg_status = (uint8_t)atoi(comma);
            }
        }
    }
    return status;
}

/**
 * @brief  获取信号强度
 * @param  signal: 信号强度指针(0-31)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetSignal(uint8_t* signal)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CSQ", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的信号强度
        char* start = strstr(gsm_rx_buffer, "+CSQ: ");
        if (start) {
            start += 6;  // 跳过"+CSQ: "
            char* comma = strchr(start, ',');
            if (comma) {
                *comma = '\0';  // 临时结束符
                *signal = (uint8_t)atoi(start);
            }
        }
    }
    return status;
}

/**
 * @brief  连接TCP服务器
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_ConnectServer(void)
{
    char command[128];
    sprintf(command, "AT+CIPSTART=\"TCP\",\"%s\",%s", GSM_SERVER_IP, GSM_SERVER_PORT);

    GSM_Status_t status = GSM_SendATCommandWithRetry(command, "CONNECT OK", GSM_CONNECT_TIMEOUT_MS, 2);
    if (status == GSM_OK) {
        gsm_state = GSM_STATE_CONNECTED;
        printf("TCP server connected successfully\r\n");
    } else {
        printf("Failed to connect to TCP server\r\n");
    }
    return status;
}

/**
 * @brief  关闭服务器连接
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CloseServer(void)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CIPCLOSE", "CLOSE OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        gsm_state = GSM_STATE_READY;
    }
    return status;
}

/**
 * @brief  设置非透传快发模式
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SetQuickSend(void)
{
    return GSM_SendATCommand("AT+CIPQSEND=1", "OK", GSM_AT_TIMEOUT_MS);
}

/**
 * @brief  发送数据
 * @param  data: 要发送的数据
 * @param  length: 数据长度
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendData(const char* data, uint16_t length)
{
    if (gsm_state != GSM_STATE_CONNECTED) {
        printf("GSM not connected, cannot send data\r\n");
        return GSM_ERROR;
    }

    // 计算实际数据长度
    uint16_t actual_length = strlen(data);

    printf("Preparing to send %d bytes of data\r\n", actual_length);

    // 发送AT+CIPSEND指令
    char command[32];
    sprintf(command, "AT+CIPSEND=%d", actual_length);

    GSM_Status_t status = GSM_SendATCommandWithRetry(command, ">", GSM_AT_TIMEOUT_MS, 2);
    if (status != GSM_OK) {
        printf("Failed to prepare data send\r\n");
        return status;
    }

    printf("Sending data: %s\r\n", data);

    // 发送实际数据
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)data, actual_length, GSM_AT_TIMEOUT_MS);

    // 等待发送完成确认
    uint16_t received_len = GSM_ReceiveData(GSM_AT_TIMEOUT_MS);
    if (received_len > 0) {
        printf("Send response: %s\r\n", gsm_rx_buffer);
        if (strstr(gsm_rx_buffer, "SEND OK") != NULL || strstr(gsm_rx_buffer, "ACCEPT") != NULL) {
            printf("Data sent successfully\r\n");
            return GSM_OK;
        }
    }

    printf("Data send failed or no confirmation received\r\n");
    return GSM_ERROR;
}

/**
 * @brief  获取模块信息
 * @param  info: 模块信息结构体指针
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetInfo(GSM_Info_t* info)
{
    GSM_Status_t status;

    // 获取模块型号
    status = GSM_GetModel(info->model);
    if (status != GSM_OK) return status;

    // 获取CCID号
    status = GSM_GetCCID(info->ccid);
    if (status != GSM_OK) return status;

    // 获取电压
    status = GSM_GetVoltage(&info->voltage);
    if (status != GSM_OK) return status;

    // 获取信号强度
    status = GSM_GetSignal(&info->signal);
    if (status != GSM_OK) return status;

    // 获取网络注册状态
    status = GSM_CheckNetwork(&info->network_reg);

    return status;
}

/**
 * @brief  获取当前状态
 * @retval GSM_State_t 当前状态
 */
GSM_State_t GSM_GetState(void)
{
    return gsm_state;
}

/**
 * @brief  带重试机制的AT指令发送
 * @param  command: AT指令
 * @param  expected_response: 期望的响应
 * @param  timeout: 超时时间(毫秒)
 * @param  retry_count: 重试次数
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendATCommandWithRetry(const char* command, const char* expected_response, uint32_t timeout, uint8_t retry_count)
{
    GSM_Status_t status;
    uint8_t attempts = 0;

    do {
        attempts++;
        printf("AT Command [%d/%d]: %s\r\n", attempts, retry_count + 1, command);

        status = GSM_SendATCommand(command, expected_response, timeout);

        if (status == GSM_OK) {
            GSM_PrintResponse(command, gsm_rx_buffer);
            return GSM_OK;
        } else {
            printf("AT Command failed (attempt %d), status: %d\r\n", attempts, status);
            if (attempts <= retry_count) {
                HAL_Delay(1000);  // 重试前等待1秒
            }
        }
    } while (attempts <= retry_count);

    printf("AT Command failed after %d attempts: %s\r\n", attempts, command);
    return status;
}

/**
 * @brief  发送AT指令并等待响应（轮询方式）
 * @param  command: AT指令
 * @param  expected_response: 期望的响应
 * @param  timeout: 超时时间(毫秒)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendATCommand(const char* command, const char* expected_response, uint32_t timeout)
{
    // 清空接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 发送AT指令
    if (strlen(command) > 0) {
        HAL_UART_Transmit(&hlpuart1, (uint8_t*)command, strlen(command), 1000);
        HAL_UART_Transmit(&hlpuart1, (uint8_t*)"\r\n", 2, 1000);
    }

    // 轮询接收响应
    uint16_t received_len = GSM_ReceiveData(timeout);
    if (received_len == 0) {
        // 超时处理
        if (strlen(command) > 0) {
            GSM_PrintError(command);
        }
        return GSM_TIMEOUT;
    }

    // 检查响应
    if (strstr(gsm_rx_buffer, expected_response) != NULL) {
        return GSM_OK;
    }
    if (strstr(gsm_rx_buffer, "ERROR") != NULL) {
        return GSM_ERROR;
    }

    return GSM_ERROR;
}

/**
 * @brief  轮询接收数据（改进版，使用阻塞接收避免丢失数据）
 * @param  timeout: 超时时间(毫秒)
 * @retval uint16_t 接收到的数据长度
 */
static uint16_t GSM_ReceiveData(uint32_t timeout)
{
    uint8_t rx_byte;
    uint16_t index = 0;
    uint32_t start_time = HAL_GetTick();
    uint32_t last_receive_time = start_time;
    uint32_t no_data_timeout = 500;  // 500ms无数据则认为接收完成

    while ((HAL_GetTick() - start_time) < timeout) {
        // 使用较短的超时时间进行阻塞接收，避免丢失数据
        if (HAL_UART_Receive(&hlpuart1, &rx_byte, 1, 50) == HAL_OK) {
            if (index < GSM_RX_BUFFER_SIZE - 1) {
                gsm_rx_buffer[index++] = rx_byte;
                gsm_rx_buffer[index] = '\0';
                last_receive_time = HAL_GetTick();
            }
        } else {
            // 如果一段时间没有接收到数据，且已经有数据，则认为接收完成
            if (index > 0 && (HAL_GetTick() - last_receive_time) > no_data_timeout) {
                break;
            }
        }
    }

    return index;
}

/**
 * @brief  计算数据长度
 * @param  data: 数据字符串
 * @retval uint16_t 计算出的长度
 */
static uint16_t GSM_CalculateDataLength(const char* data)
{
    return strlen(data);
}

/**
 * @brief  打印AT指令响应
 * @param  command: AT指令
 * @param  response: 响应内容
 */
static void GSM_PrintResponse(const char* command, const char* response)
{
    printf("AT Response [%s]: %s\r\n", command, response);
}

/**
 * @brief  打印错误信息
 * @param  command: 失败的AT指令
 */
static void GSM_PrintError(const char* command)
{
    printf("AT Command ERROR: %s\r\n", command);
}

/* USER CODE BEGIN 2 */

/* USER CODE END 2 */
