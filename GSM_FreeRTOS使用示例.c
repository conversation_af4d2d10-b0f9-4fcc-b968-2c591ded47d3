/**
 * @file    GSM_FreeRTOS使用示例.c
 * @brief   GSM模块在FreeRTOS环境下的使用示例
 * @note    此文件仅作为使用示例，不包含在项目中
 */

#include "GSM.h"
#include "cmsis_os.h"
#include <stdio.h>

/* 任务句柄 */
TaskHandle_t gsm_task_handle = NULL;

/* 任务参数 */
#define GSM_TASK_STACK_SIZE    1024
#define GSM_TASK_PRIORITY      3

/**
 * @brief  GSM任务函数
 * @param  argument: 任务参数
 */
void GSM_Task(void *argument)
{
    GSM_Status_t status;
    GSM_Info_t gsm_info;

    printf("GSM Task Started\r\n");

    // 1. 初始化GSM模块
    printf("Initializing GSM module...\r\n");
    status = GSM_Init();
    if (status != GSM_OK) {
        printf("GSM Init Failed! Status: %d\r\n", status);
        vTaskDelete(NULL);
        return;
    }
    printf("GSM Init Success!\r\n");

    // 2. 获取模块信息
    printf("Getting module information...\r\n");
    status = GSM_GetInfo(&gsm_info);
    if (status == GSM_OK) {
        printf("Model: %s\r\n", gsm_info.model);
        printf("CCID: %s\r\n", gsm_info.ccid);
        printf("Voltage: %d mV\r\n", gsm_info.voltage);
        printf("Signal: %d\r\n", gsm_info.signal);
        printf("Network Reg: %d\r\n", gsm_info.network_reg);
    }

    // 3. 主循环 - 定期发送数据
    while (1) {
        // 检查网络状态
        uint8_t network_status;
        status = GSM_CheckNetwork(&network_status);
        if (status == GSM_OK && network_status == 1) {
            printf("Network registered, connecting to server...\r\n");

            // 连接服务器
            status = GSM_ConnectServer();
            if (status == GSM_OK) {
                printf("Connected to server successfully!\r\n");

                // 设置快发模式
                status = GSM_SetQuickSend();
                if (status == GSM_OK) {
                    // 发送数据
                    const char* test_data = "HY122S+11957.70898+3016.57080+052857.270625+38.9+1+6+1.7+4.20+55.6+2.9+3.0+3.8+-1.9+0.0+0.00+-128+89860316249511500582+E";
                    status = GSM_SendData(test_data, strlen(test_data));
                    if (status == GSM_OK) {
                        printf("Data sent successfully!\r\n");
                    } else {
                        printf("Send data failed! Status: %d\r\n", status);
                    }
                }

                // 关闭连接
                GSM_CloseServer();
            } else {
                printf("Connect to server failed! Status: %d\r\n", status);
            }
        } else {
            printf("Network not registered or check failed\r\n");
        }

        // 等待30秒后再次发送
        vTaskDelay(pdMS_TO_TICKS(30000));
    }
}

/**
 * @brief  创建GSM任务
 */
void GSM_CreateTask(void)
{
    // 创建GSM任务
    BaseType_t result = xTaskCreate(
        GSM_Task,                    // 任务函数
        "GSM_Task",                  // 任务名称
        GSM_TASK_STACK_SIZE,         // 堆栈大小
        NULL,                        // 任务参数
        GSM_TASK_PRIORITY,           // 任务优先级
        &gsm_task_handle             // 任务句柄
    );

    if (result == pdPASS) {
        printf("GSM Task created successfully\r\n");
    } else {
        printf("Failed to create GSM Task\r\n");
    }
}

/**
 * @brief  在main函数中调用GSM任务创建
 * @note   在main.c的main函数中添加以下代码：
 */
/*
int main(void)
{
    // ... 其他初始化代码 ...

    // 启动FreeRTOS调度器
    osKernelInitialize();

    // 创建GSM任务
    GSM_CreateTask();

    // 启动调度器
    osKernelStart();

    while (1) {
        // 不应该执行到这里
    }
}
*/

/**
 * @brief  获取信号强度示例（在任务中调用）
 */
void GSM_GetSignalInTask(void)
{
    uint8_t signal;
    GSM_Status_t status = GSM_GetSignal(&signal);

    if (status == GSM_OK) {
        printf("Signal strength: %d\r\n", signal);

        // 信号强度判断
        if (signal >= 20) {
            printf("Signal: Excellent\r\n");
        } else if (signal >= 15) {
            printf("Signal: Good\r\n");
        } else if (signal >= 10) {
            printf("Signal: Fair\r\n");
        } else {
            printf("Signal: Poor\r\n");
        }
    } else {
        printf("Get signal failed! Status: %d\r\n", status);
    }
}

/**
 * @brief  获取电压信息示例（在任务中调用）
 */
void GSM_GetVoltageInTask(void)
{
    uint16_t voltage;
    GSM_Status_t status = GSM_GetVoltage(&voltage);

    if (status == GSM_OK) {
        printf("Battery voltage: %d mV\r\n", voltage);

        // 电压判断
        if (voltage >= 3500) {
            printf("Battery: High\r\n");
        } else if (voltage >= 3000) {
            printf("Battery: Medium\r\n");
        } else {
            printf("Battery: Low\r\n");
        }
    } else {
        printf("Get voltage failed! Status: %d\r\n", status);
    }
}

/**
 * @brief  错误处理示例
 */
void GSM_ErrorHandlingExample(void)
{
    GSM_Status_t status = GSM_SomeFunction();

    switch (status) {
        case GSM_OK:
            printf("Operation successful\r\n");
            break;
        case GSM_ERROR:
            printf("Operation failed\r\n");
            // 可以在这里添加重试逻辑
            break;
        case GSM_TIMEOUT:
            printf("Operation timeout\r\n");
            // 可以在这里添加超时处理逻辑
            break;
        case GSM_BUSY:
            printf("Module busy\r\n");
            // 可以在这里添加等待逻辑
            vTaskDelay(pdMS_TO_TICKS(1000));
            break;
        default:
            printf("Unknown error\r\n");
            break;
    }
}
